import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Avatar,
  Chip,
  InputAdornment,
  useTheme,
  Grid,
  IconButton,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  Button,
  Divider,
} from '@mui/material';
import { Icon } from '@iconify/react';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';
import { useRouter } from 'src/routes/hooks';
import { paths } from 'src/routes/paths';
import { useAgentView, Template } from './use-templates-view';
import { AppTable } from 'src/components/table';

// Agent Card Component
const AgentCard = ({ agent, id }) => {
  const theme = useTheme();
  const router = useRouter();

  const handleCloneTemplate = (id) => {
    router.push(paths.dashboard.agents.clone?.(id));
  };

  return (
    <Card
      sx={{
        p: 3,
        height: '100%',
        border: '1px solid',
        borderColor: 'divider',
        background: (theme) => `linear-gradient(145deg, ${theme.palette.background.paper} 0%, ${theme.palette.background.default} 100%)`,
        backdropFilter: 'blur(10px)',
        borderRadius: 3,
        position: 'relative',
        overflow: 'hidden',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        cursor: 'pointer',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: (theme) => `${theme.shadows[8]}, 0 0 0 1px ${theme.palette.primary.main}20`,
          borderColor: 'primary.main',
        },
      }}
    >
      <CardContent sx={{ p: 0 }}>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
          <Box display="flex" alignItems="center" gap={1}>
            <Typography variant="h6" fontWeight={600} color="text.primary">
              {agent.name}
            </Typography>
            <Chip
              label={agent.type}
              size="small"
              sx={{
                bgcolor: 'rgba(163, 139, 233, 0.33)',
                color: 'inherit',
                fontWeight: 600,
                fontSize: '0.75rem',
                borderRadius: '6px',
              }}
            />
          </Box>
          <IconButton size="small">
            <Icon icon="eva:more-horizontal-fill" width={18} height={18} />
          </IconButton>
        </Box>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {agent.description}
        </Typography>
        <AppButton
          variant="outlined"
          color="primary"
          fullWidth
          onClick={() => handleCloneTemplate(id)}
          label=" Clone Template"
        />
      </CardContent>
    </Card>
  );
};

export function AgentsView() {
  const {
    filteredAgents,
    isLoading,
    error,
    refetch,
    searchQuery,
    selectedTypeTab,
    handleSearch,
  } = useAgentView();

  if (isLoading) {
    return (
      <Box sx={{ width: '100%', minHeight: '100vh', bgcolor: 'background.default', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <CircularProgress size={40} />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ width: '100%', minHeight: '100vh', bgcolor: 'background.default' }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          Failed to load templates. Please try again.
        </Alert>
        <AppButton variant="outlined" onClick={() => refetch()} label="Retry" />
      </Box>
    );
  }

  // Separate agents based on visibility
  const publicAgents = filteredAgents.filter(agent => agent.visibility === 'PUBLIC');
  const privateAgents = filteredAgents.filter(agent => agent.visibility === 'PRIVATE');

  return (
    <Box sx={{ width: '100%', minHeight: '100vh', bgcolor: 'background.default', p: 4 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Typography variant="h3" fontWeight={800} color="text.primary">Teams</Typography>
        <AppButton variant="contained" startIcon={<Icon icon="eva:plus-fill" width={20} height={20} />} label="New Agent's Template" />
      </Box>
      <Divider sx={{ mb: 2 }} />

      {/* Tab Navigation */}
      <Tabs value={selectedTypeTab} onChange={(e, newValue) => handleSearch(newValue)} variant="scrollable">
        <Tab label="Public Templates" />
        <Tab label="Private Templates" />
      </Tabs>

      {/* Tab Content */}
      {selectedTypeTab === 0 ? (
        <Grid container spacing={2}>
          {publicAgents.map(agent => (
            <Grid item xs={12} sm={6} lg={4} key={agent.id}>
              <AgentCard agent={agent} id={agent.id} />
            </Grid>
          ))}
        </Grid>
      ) : (
        <AppTable
          title="Private Templates"
          Labels={['Name', 'Date Created', 'Category', 'Status']}
          dataCount={privateAgents.length}
          data={privateAgents}
          isLoading={isLoading}
          noDataLabel="No private templates found"
        />
      )}
    </Box>
  );
}